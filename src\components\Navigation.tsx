import { useState, useEffect, useRef } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Link, useLocation } from 'react-router-dom';
import { ChevronDown, Building2, Home, Sparkles, Users, Briefcase, Heart, Menu, X } from 'lucide-react';

interface NavigationProps {
  scrollToSection: (id: string) => void;
}

const Navigation = ({ scrollToSection }: NavigationProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();
  const navRef = useRef<HTMLElement>(null);
  const servicesDropdownRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const leaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close mobile menu when navigating
  useEffect(() => {
    if (isMenuOpen) {
      setIsMenuOpen(false);
    }
  }, [location.pathname]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
        setIsServicesOpen(false);
      }
    };

    if (isMenuOpen || isServicesOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen, isServicesOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMenuOpen(false);
        setIsServicesOpen(false);
      }
    };

    if (isMenuOpen || isServicesOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isMenuOpen, isServicesOpen]);

  const handleNavClick = (sectionId: string) => {
    if (location.pathname !== '/' && ['home', 'services', 'testimonials', 'contact'].includes(sectionId)) {
      window.location.href = `/#${sectionId}`;
      return;
    }
    scrollToSection(sectionId);
    setIsMenuOpen(false);
    setIsServicesOpen(false);
  };

  const toggleServicesDropdown = () => {
    setIsServicesOpen(!isServicesOpen);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    setIsServicesOpen(false); // Close services dropdown when opening/closing main menu
  };

  // Enhanced hover handlers with delay for better UX
  const handleServicesMouseEnter = () => {
    // Clear any existing leave timeout
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
      leaveTimeoutRef.current = null;
    }

    // Set hover timeout for smooth entry
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    hoverTimeoutRef.current = setTimeout(() => {
      setIsServicesOpen(true);
    }, 100); // Small delay for smooth hover entry
  };

  const handleServicesMouseLeave = () => {
    // Clear any existing hover timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    // Set leave timeout to allow user to move to dropdown
    leaveTimeoutRef.current = setTimeout(() => {
      setIsServicesOpen(false);
    }, 300); // 300ms buffer to move to dropdown items
  };

  const handleDropdownMouseEnter = () => {
    // Clear leave timeout when entering dropdown
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
      leaveTimeoutRef.current = null;
    }
  };

  const handleDropdownMouseLeave = () => {
    // Close dropdown when leaving dropdown area
    leaveTimeoutRef.current = setTimeout(() => {
      setIsServicesOpen(false);
    }, 150); // Shorter delay when leaving dropdown
  };

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      if (leaveTimeoutRef.current) {
        clearTimeout(leaveTimeoutRef.current);
      }
    };
  }, []);

  const navLinks = [
    { name: 'Startseite', path: '/', sectionId: 'home' },
    { name: 'Termin buchen', path: '/booking', isButton: true },
    { name: 'Referenzen', path: '/', sectionId: 'testimonials' },
    { name: 'Kontakt', path: '/', sectionId: 'contact' },
  ];

  const serviceLinks = [
    { name: 'Hotelzimmerreinigung', path: '/services/hotelzimmerreinigung', icon: Building2, description: 'Höchste Hygienestandards' },
    { name: 'Teppichreinigung', path: '/services/teppichreinigung', icon: Home, description: 'Tiefenreinigung & Fleckenentfernung' },
    { name: 'Bodenreinigung', path: '/services/bodenreinigung', icon: Sparkles, description: 'Hartböden, Fliesen, Laminat' },
    { name: 'Gemeinschaftsräume', path: '/services/gemeinschaftsraeume', icon: Users, description: 'Treppenhäuser & Flure' },
    { name: 'Büroreinigung', path: '/services/bueroreinigung', icon: Briefcase, description: 'Arbeitsplätze & Büroflächen' },
    { name: 'Krankenhausreinigung', path: '/services/krankenhausreinigung', icon: Heart, description: 'Medizinische Einrichtungen' },
  ];

  return (
    <>
      {/* Desktop Navigation */}
      {!isMobile && (
        <nav
          id="main-navigation"
          ref={navRef}
          className="fixed top-6 z-50 animate-fade-in suz-navigation-enhanced"
          role="navigation"
          aria-label="Hauptnavigation"
          style={{
            left: '50%',
            transform: 'translateX(-50%)',
            width: 'auto',
            maxWidth: 'calc(100vw - 2rem)',
            minWidth: 'fit-content',
            display: 'block',
            overflow: 'visible', /* Ensure dropdown is not clipped */
          }}
        >
          <div className="suz-card-glass px-4 sm:px-6 md:px-8 py-3 sm:py-4 rounded-full border border-white/30 shadow-xl" style={{ overflow: 'visible', position: 'relative', zIndex: 100 }}>
            <div className="flex items-center justify-center space-x-1 sm:space-x-2 md:space-x-4 lg:space-x-6 xl:space-x-8">
              {navLinks.map((link) => (
                link.name === 'Startseite' && location.pathname === '/' ? (
                  <button
                    key={link.name}
                    type="button"
                    onClick={() => handleNavClick(link.sectionId!)}
                    className="suz-nav-link suz-focus-ring whitespace-nowrap"
                    aria-label={`Zur ${link.name} navigieren`}
                  >
                    {link.name}
                  </button>
                ) : link.name === 'Startseite' ? (
                  <Link
                    key={link.name}
                    to={link.path}
                    className="suz-nav-link suz-focus-ring whitespace-nowrap"
                    aria-label={`Zur ${link.name} navigieren`}
                  >
                    {link.name}
                  </Link>
                ) : link.name === 'Termin buchen' ? (
                  <Link
                    key={link.name}
                    to={link.path}
                    className="suz-nav-link-cta suz-focus-ring whitespace-nowrap"
                    aria-label={link.name}
                  >
                    {link.name}
                  </Link>
                ) : link.sectionId ? (
                  <button
                    key={link.name}
                    type="button"
                    onClick={() => handleNavClick(link.sectionId)}
                    className="suz-nav-link suz-focus-ring whitespace-nowrap"
                    aria-label={`Zu ${link.name} navigieren`}
                  >
                    {link.name}
                  </button>
                ) : null
              ))}

              {/* Services Dropdown */}
              <div
                className="relative group"
                onMouseEnter={handleServicesMouseEnter}
                onMouseLeave={handleServicesMouseLeave}
              >
                <button
                  type="button"
                  className="suz-services-button"
                  onClick={toggleServicesDropdown}
                  aria-expanded={isServicesOpen ? 'true' : 'false'}
                  aria-haspopup="menu"
                  aria-label="Leistungen anzeigen"
                >
                  Leistungen
                  <ChevronDown className={`chevron ${isServicesOpen ? 'rotate-180' : ''}`} />
                </button>

                <div
                  ref={servicesDropdownRef}
                  className={`suz-services-dropdown ${isServicesOpen ? 'show' : ''}`}
                  role="menu"
                  aria-label="Leistungen Dropdown"
                  onMouseEnter={handleDropdownMouseEnter}
                  onMouseLeave={handleDropdownMouseLeave}
                >
                  {serviceLinks.map((service) => {
                    const Icon = service.icon;
                    return (
                      <Link
                        key={service.name}
                        to={service.path}
                        className="suz-services-dropdown-item"
                        role="menuitem"
                        onClick={() => setIsServicesOpen(false)}
                      >
                        <Icon className={`suz-services-dropdown-icon ${service.name.includes('Hotel') ? 'text-blue-600' : service.name.includes('Teppich') ? 'text-green-600' : service.name.includes('Boden') ? 'text-purple-600' : service.name.includes('Gemeinschafts') ? 'text-orange-600' : service.name.includes('Büro') ? 'text-blue-600' : service.name.includes('Krankenhaus') ? 'text-red-600' : ''}`} />
                        <div className="suz-services-dropdown-content">
                          <div className="suz-services-dropdown-title">{service.name}</div>
                          <div className="suz-services-dropdown-description">{service.description}</div>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </nav>
      )}

      {/* Mobile Navigation */}
      {isMobile && (
        <>
          {/* Mobile Menu Button */}
          <div
            className="fixed top-6 right-6 z-50 animate-fade-in"
            role="navigation"
            aria-label="Mobile Navigation"
          >
            <div className="suz-card-glass px-4 py-3 rounded-full border border-white/30 shadow-xl">
              <button
                type="button"
                onClick={toggleMenu}
                className="suz-mobile-menu-button suz-focus-ring"
                aria-label={isMenuOpen ? 'Menü schließen' : 'Menü öffnen'}
                aria-expanded={isMenuOpen ? 'true' : 'false'}
                aria-controls="mobile-menu"
              >
                {isMenuOpen ? (
                  <X className="w-6 h-6 text-slate-100" />
                ) : (
                  <Menu className="w-6 h-6 text-slate-100" />
                )}
              </button>
            </div>
          </div>

          {/* Mobile Menu Overlay */}
          {isMenuOpen && (
            <div className="suz-mobile-menu-overlay" aria-hidden="true">
              <div className="suz-mobile-menu-backdrop" onClick={toggleMenu}></div>
            </div>
          )}

          {/* Mobile Menu */}
          <div
            id="mobile-menu"
            className={`suz-mobile-menu ${isMenuOpen ? 'suz-mobile-menu-open' : 'suz-mobile-menu-closed'}`}
            aria-hidden={!isMenuOpen ? 'true' : 'false'}
          >
            <div className="suz-mobile-menu-content">
              {navLinks.map((link) => (
                link.name === 'Startseite' && location.pathname === '/' ? (
                  <button
                    key={link.name}
                    type="button"
                    onClick={() => handleNavClick(link.sectionId!)}
                    className="suz-mobile-nav-link suz-focus-ring"
                    aria-label={`Zur ${link.name} navigieren`}
                  >
                    {link.name}
                  </button>
                ) : link.name === 'Startseite' ? (
                  <Link
                    key={link.name}
                    to={link.path}
                    className="suz-mobile-nav-link suz-focus-ring"
                    onClick={() => setIsMenuOpen(false)}
                    aria-label={`Zur ${link.name} navigieren`}
                  >
                    {link.name}
                  </Link>
                ) : link.name === 'Termin buchen' ? (
                  <Link
                    key={link.name}
                    to={link.path}
                    className="suz-mobile-nav-link-cta suz-focus-ring"
                    onClick={() => setIsMenuOpen(false)}
                    aria-label={link.name}
                  >
                    🗓️ {link.name}
                  </Link>
                ) : link.sectionId ? (
                  <button
                    key={link.name}
                    type="button"
                    onClick={() => handleNavClick(link.sectionId)}
                    className="suz-mobile-nav-link suz-focus-ring"
                    aria-label={`Zu ${link.name} navigieren`}
                  >
                    {link.name}
                  </button>
                ) : null
              ))}

              {/* Mobile Services Section */}
              <button
                type="button"
                onClick={toggleServicesDropdown}
                className="suz-mobile-nav-link suz-focus-ring flex items-center justify-between w-full"
                aria-label="Leistungen anzeigen"
                aria-expanded={isServicesOpen ? 'true' : 'false'}
              >
                Leistungen
                <ChevronDown className={`w-4 h-4 transition-transform ${isServicesOpen ? 'rotate-180' : ''}`} />
              </button>
              
              <div className={`suz-services-dropdown ${isServicesOpen ? 'show' : ''}`} role="menu">
                {serviceLinks.map((service) => {
                  const Icon = service.icon;
                  return (
                    <Link
                      key={service.name}
                      to={service.path}
                      className="suz-services-dropdown-item"
                      role="menuitem"
                      onClick={() => {
                        setIsMenuOpen(false);
                        setIsServicesOpen(false);
                      }}
                    >
                      <Icon className={`suz-services-dropdown-icon ${service.name.includes('Hotel') ? 'text-blue-600' : service.name.includes('Teppich') ? 'text-green-600' : service.name.includes('Boden') ? 'text-purple-600' : service.name.includes('Gemeinschafts') ? 'text-orange-600' : service.name.includes('Büro') ? 'text-blue-600' : service.name.includes('Krankenhaus') ? 'text-red-600' : ''}`} />
                      <div className="suz-services-dropdown-content">
                        <div className="suz-services-dropdown-title">{service.name}</div>
                        <div className="suz-services-dropdown-description">{service.description}</div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default Navigation;
